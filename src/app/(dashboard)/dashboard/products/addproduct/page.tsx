"use client";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"
import { useRef, useState } from "react";
import { FaPersonHalfDress } from "react-icons/fa6";
import { GiHamburgerMenu } from "react-icons/gi";
import { <PERSON>m<PERSON><PERSON> } from "react-icons/im";
import { MdDelete, MdModeEditOutline } from "react-icons/md";
import { RxCross1 } from "react-icons/rx";

type ProductDetails = {
    category: string,
    class : string[]
}

type DressInput = {
    [key:number] : string
}

const manrope = Manrope({
    subsets:['latin']
});

const anton = Anton({
    subsets: ['latin'],
    weight: "400"
})


export default function AddProducts(){
    const [productDetails,setProductDetails] = useState<ProductDetails[]>([]);
    const categoryRef = useRef<HTMLInputElement | null>(null);
    const [dressInput,setDressInput] = useState<DressInput>({});
    const [accordionArr,setAccordionArr] = useState<number[]>([]);

    const addNewCategory=()=>{
        if(categoryRef?.current?.value){
            const copy = productDetails;
            const update = [...copy,{category:categoryRef.current.value,class:[]}];

            setProductDetails(update)

            categoryRef.current.value = "";
        }
    }

    const handleDressInput=(key:number,event: React.ChangeEvent<HTMLInputElement>)=>{
        const {value} = event.target;

        setDressInput(prev=>({...prev,[key]:value}));
    }

    const addNewDress=(serial:number)=>{
            const exist = dressInput[serial]?.trim();
            
            if(!exist) return;
            
            const copy = productDetails[serial];
            const update = {...copy,class:[...copy.class,exist]};

            const newProductDetails = productDetails;
            
            newProductDetails[serial] = update;

            setProductDetails(newProductDetails);

            setDressInput(prev=>({...prev,[serial]:""}))
    }

    const removeDress=(primaryIndex:number,secondaryIndex:number)=>{
        const copy = productDetails;
        const update = copy.map((items,index)=>{
            if(index === primaryIndex){
                return {...items,class:items.class.filter((_,subIndex)=> subIndex !== secondaryIndex)}
            }else{
                return items;
            }
        });

        setProductDetails(update);
    }

    const removeCategory=(serial:number)=>{
        const copy = productDetails;
        const update = copy.filter((_,index)=> index !== serial);

        setProductDetails(update);
    }

    const toggleAccordion=(serial:number)=>{
        const copy = accordionArr;
        let update;

        if(copy.includes(serial)){
            update = copy.filter(items=>items !== serial)
        }else{
            update = [...copy,serial]
        }

        setAccordionArr(update)
    }
    return (
        <>
        <section className="mt-5 px-5">
            <div className="flex flex-row justify-end gap-x-5">
                <div className="h-[50px] w-[40%]">
                    <input type="text" className={`${manrope} h-full w-full border-1 border-gray-300 rounded-lg px-5 capitalize text-black font-medium text-base placeholder:text-black/20 focus:outline-gray-500/50`} placeholder="add your category" ref={categoryRef}/>
                </div>

                <div>
                    <button type="button" className={`${manrope} text-base font-bold uppercase h-[50px] px-5 bg-[#2ecc71] rounded-lg text-white shadow-[5px_5px_2px_#16a085] transition-all duration-200 ease-linear hover:shadow-[1px_1px_2px_#16a085] hover:scale-95 hover:cursor-pointer active:bg-[#16a085]`} onClick={addNewCategory}>
                        add new
                    </button>
                </div>
            </div>
        </section>

        <section className="px-5 mt-5 py-20">
            <div className="grid grid-cols-3 gap-x-5">
                <div className="col-span-2 rounded-xl px-5">
                    {
                        productDetails.map((items,index)=>{
                            return <div className={`py-3 transition-all duration-500 ease-linear ${accordionArr.includes(index) ?"max-h-[450px] overflow-y-scroll dashboardCategoryScrollbar":"max-h-[63px] overflow-hidden"}`} key={index}>
                                <button className="absolute -right-10 top-10 text-rose-300 rotate-[45deg] transition-all duration-200 ease-linear hover:rotate-0 hover:text-rose-500 hover:cursor-pointer" onClick={()=>{removeCategory(index)}}>
                                    <ImCross />
                                </button>

                        <div className="grid grid-cols-2 items-center border border-black border-t-0 border-r-0 border-l-0 py-2">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h2 className={`${anton} uppercase text-2xl font-bold bg-black text-white rounded-xl px-5`}>
                                    {items.category}
                                    </h2>
                                </div>
                                
                                <div>
                                    <button className="hover:cursor-pointer" onClick={()=>{toggleAccordion(index)}}>
                                    {
                                        !accordionArr.includes(index) ?
                                        <GiHamburgerMenu />:
                                        <ImCross />
                                    }
                                </button>
                                </div>
                            </div>
            
                            <div className="flex flex-row items-center justify-end gap-x-3">
                                <div className="h-[20px]">
                                    <input type="text" className={`${manrope} h-full w-full border border-black/20 rounded-md placeholder:text-sm pl-4 focus:outline-black/40 text-black/40 font-medium`} placeholder="New dress class" value={dressInput[index] || ""} onChange={(event)=>{handleDressInput(index,event)}}/>
                                </div>
                                <div>
                                    <button className={`${manrope} h-5 flex items-center px-4 border border-black rounded-md text-black font-bold hover:cursor-pointer transition-all duration-200 ease-linear hover:bg-black hover:text-white`} onClick={()=>{addNewDress(index)}}>
                                        add
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="mt-5">
                            <table className="table-auto w-full border border-black/10">
                                <thead className="bg-black text-white">
                                    <tr className={`${manrope} text-xl text-left capitalize`}>
                                        <th className="px-4 border border-white border-t-0 border-b-0 border-l-0">
                                            id
                                        </th>
                                        <th className="px-4">
                                            dress style
                                        </th>
                                        <th className="px-4 border border-black border-t-0 border-b-0 border-l-0">

                                        </th>
                                    </tr>
                                </thead>

                                <tbody>
                                    {
                                        items.class.map((subItems,subIndex)=>{
                                            return <tr className={`${manrope} font-medium text-base h-[60px] capitalize text-black/60 even:bg-white`} key={subIndex}>
                                        <td className="px-4">
                                            {subIndex}
                                        </td>
                                        <td className="px-4">
                                            {subItems}
                                        </td>
                                        <td className="px-4 text-right space-x-4">
                                            <button className="p-2 bg-[#2ecc71] rounded-xl text-white transition-all duration-200 ease-linear hover:bg-[#27ae60] shadow-[3px_3px_2px_#2ecc71]">
                                                <FaPersonHalfDress />
                                            </button>
                                            <button className="p-2 bg-[#f39c12] rounded-xl text-white transition-all duraiton-200 ease-linear hover:bg-[#e67e22] shadow-[3px_3px_2px_#f39c12]">
                                                <MdModeEditOutline />
                                            </button>
                                            <button className="p-2 bg-[#e74c3c] rounded-xl text-white transition-all duration-200 ease-linear hover:bg-[#c0392b] shadow-[3px_3px_2px_#e74c3c]" onClick={()=>{removeDress(index,subIndex)}}>
                                                <MdDelete />
                                            </button>
                                        </td>
                                    </tr>
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                        })
                    }
                </div>

                <div>

                </div>
            </div>
        </section>
        </>
    )
}